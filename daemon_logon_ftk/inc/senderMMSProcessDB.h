#ifndef _SENDER_MMS_PROCESS_H_
#define _SENDER_MMS_PROCESS_H_

#include <string>
#include "stdafx.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "fifoInfo.h"
#include "logonUtil.h"
#include "kssocket.h"
#include "adminInfo.h"
#include "adminUtil.h"
#include "senderDbInfo.h"
#include "processInfo.h"
#include "monitor.h"
#include "ksconfig.h"
#include "senderInfo.h"
//#include "mmsPacketUtil.h"
#include "mmsPacketBase.h"
#include "mmsPacketSend.h"
#include "dbUtil.h"
#include "mmsFileProcess.h"
#include "DatabaseORA_MMS.h"
#include "checkCallback.h"

#include "Curl.h"
#include "json.h"

using namespace std;
#define TELCO_SKT "QUEUE_SKT"
#define TELCO_KTF "QUEUE_KTF"
#define TELCO_SSN "QUEUE_SKT"
#define TELCO_LGT "QUEUE_LGT" 
#define TELCO_SKB "QUEUE_SKB_BCK"
#define TELCO_KTC "QUEUE_KTC"

class CConfigSender {
    public:
        char logonDBName[64];
        char monitorName[64];
        char domainPath[64];
        int socketLinkTimeOut;
        int dbRequestTimeOut;
        char ContentPath[64];
        char dbuid[64];
        char dbdsn[64];
        int dbMmsIdHeader;
        int gwAppHeader;
        int gwAddTelco;
		char img_target_url[128];
		char wide_img_target_url[128];
	char encryptKey[64];
};

typedef struct st_img_res
{
	string code;
	string message;
	string img_url;
}ST_IMG_RES;


CConfigSender gConf;
CSenderInfo gSenderInfo;
KSKYB::CDatabaseORA g_oracle;

CCheckCallback checkCallback;

int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];

class SenderProcess{
	public:
	SenderProcess();
	~SenderProcess(){
	}
	public:
	void SenderMain(int sockfd,CLogonDbInfo& logonDbInfo);
	private:
	int classifyS(
      CMonitor& monitor,
      CProcessInfo& processInfo,
      CLogonDbInfo& logonDbInfo,
      CKSSocket& db,
      CKSSocket& hRemoteSock
	);
	//* < brief ??? ???? ??
	int SenderLimit(CLogonDbInfo& logonDbInfo);
	int LimitCheck(CLogonDbInfo& logonDbInfo);
	//
	public:
	char szLimitTime[16];
	char szLimitCurTime[16];
	int nCurAccCnt;
	bool bDayWarnCheck;
	bool bMonWarnCheck;
};
void logPrintS(int type, const char *format, ...);
int recvLink(CKSSocket& hRemoteSock,char* buff);
int recvSMS(CLogonDbInfo& logonDbInfo,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        char* buff);

int recvMMS(CLogonDbInfo& logonDbInfo,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        char* buff);
char*  get010Telco(char* key);


int getMMSID2DB(CSenderDbMMSID& senderDbMMSID,char* cid);
int getCTNID2DB(CSenderDbMMSID& senderDbMMSID);
int setMMSTBL2DB(long long nMMSId,int ctnid, int priority, CBcastData* pBcastData = NULL );
int setMMSMSG2DB(char* szCid, long long nMMSId,int ctnid,CMMSFileProcess& mmsFileProcess, int priority, CBcastData* pBcastData = NULL );
int setMMSMSG2DB_TALK(long long nMMSId,CMMSPacketSend &mmsPacketSend, int priority);
int setMMSMSG_TALK(CSenderDbMMSMSG_TALK &que_data);
int setMMSCTNTBL2DB(CMMSFileProcess& mmsFileProcess);
int setRPTTBL2DB(long long nMMSId, int ctnid, int priority, int _code, char *code_text);
int loadDialCodeAll();
int sendPong(CKSSocket& hRemoteSock);
int sendAck(CKSSocket& hRemoteSock,CMMSPacketSend& mmsPacketSend,int nCode,int ctnid,string strDesc);
int send_image_url_ack (CKSSocket& hRemoteSock,
                        CMMSPacketSend& mmsPacketSend,
                        string strCode,
                        string strDesc,
                        string strImgLink);
int setMMSRPTTBL(int type, long long nMMSId, CMMSPacketSend& mmsPacketSend, int nResCode,char* res_text,char* cid);
void writeLogMMSData(CMMSPacketSend& mmsPacketSend,long long mmsid, int ctnid);

int configParse(char* file);

void viewPackSender(char *a,int n);

char* getTelcoQName(char* dstaddr,char* telco = NULL );

int getTelcoId(int imgCnt, char* szTelco, int nColorYN);
int getTelcoId(int imgCnt, char* szTelco, char *szQType);

bool bSActive = true;
time_t SThisT,SLastTLink;
time_t monLastT;
int sum=0;
char szSenderID[16];
char _DATALOG[64];
char _MONILOG[64];

char senderDbDomainName[64];

#endif

