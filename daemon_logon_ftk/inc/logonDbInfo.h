#ifndef _LOGON_DB_INFO_H_
#define _LOGON_DB_INFO_H_

class CLogonDbInfo {
    public:
        char szCID[10+1];
        char szPWD[10+1];
        char szSIP[100+1];
        char szAP<PERSON><PERSON><PERSON>[16];
        char szServerInfo[128];
        int nRptNoDataSleep;
        char sz<PERSON><PERSON><PERSON>ame[64];
        char szR<PERSON><PERSON><PERSON>ame[64];
        char szS<PERSON><PERSON>B<PERSON>ame[64];
        char szReportDBName[64];
        char szLogFilePath[128];
        char szReserve[128];
        int nmPID, nmJOB, nmPRT, nmCNT, nmRST;
        int nUrlJob;
        int nRptWait;
				char classify; /* classify sender or report : S:sender R:report */
        char szIP[16];
        int errno;
        char szLogPath[64];
        char szDomainPath[64];
        
      ////////////////////////////////////////////////////////////////////////////////
        //* < brief 발송 제한 관련 변수 선언
				char szLimitType[1+1];
				char szLimitFlag[1+1];
				int nDayWarnCnt;
				int nMonWarnCnt;
				int nDayLimitCnt;
				int nMonLimitCnt;
				int nDayAccCnt;
				int nMonAccCnt;
				int nCurAccCnt;
				char szDate[16];
};

#endif


