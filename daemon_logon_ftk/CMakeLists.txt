cmake_minimum_required(VERSION 3.16)
project(daemon_logon_ftk)

set(CMAKE_CXX_STANDARD 11)

# CLion 빌드 플래그 설정 (독립 빌드 시)
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()

if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "daemon_logon_ftk: CLion build mode enabled")
endif()

# 컴파일 데이터베이스 생성 (IDE/에디터의 코드 탐색 기능을 위해)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# OS 및 OpenSSL 버전 감지
execute_process(COMMAND lsb_release -rs OUTPUT_VARIABLE OS_VERSION OUTPUT_STRIP_TRAILING_WHITESPACE ERROR_QUIET)
execute_process(COMMAND cat /etc/redhat-release OUTPUT_VARIABLE REDHAT_RELEASE OUTPUT_STRIP_TRAILING_WHITESPACE ERROR_QUIET)

# OpenSSL 버전 확인
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(OPENSSL openssl)
    if(OPENSSL_FOUND)
        set(OPENSSL_VERSION ${OPENSSL_VERSION})
    endif()
endif()

# OpenSSL 헤더에서 버전 직접 확인
if(NOT OPENSSL_VERSION)
    find_path(OPENSSL_INCLUDE_DIR openssl/opensslv.h)
    if(OPENSSL_INCLUDE_DIR)
        file(STRINGS "${OPENSSL_INCLUDE_DIR}/openssl/opensslv.h" 
             OPENSSL_VERSION_LINE REGEX "^#define OPENSSL_VERSION_NUMBER")
        string(REGEX MATCH "0x[0-9a-fA-F]+" OPENSSL_VERSION_HEX "${OPENSSL_VERSION_LINE}")
        
        # OpenSSL 버전 분류
        if(OPENSSL_VERSION_HEX VERSION_GREATER_EQUAL "0x30000000")
            set(OPENSSL_MAJOR_VERSION 3)
        elseif(OPENSSL_VERSION_HEX VERSION_GREATER_EQUAL "0x10100000")
            set(OPENSSL_MAJOR_VERSION 1.1)
        else()
            set(OPENSSL_MAJOR_VERSION 1.0)
        endif()
    endif()
endif()

# OS별 컴파일 플래그 설정
# 모든 환경에서 OpenSSL 호환성 문제를 해결하기 위해 강제로 레거시 설정 적용
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall -std=gnu++98 -w -Wno-deprecated-declarations")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED")

if(REDHAT_RELEASE MATCHES "CentOS.*6\\.")
    # CentOS 6.x 환경 (OpenSSL 1.0.1)
    message(STATUS "Building for CentOS 6.x with OpenSSL 1.0.1")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DCENTOS_6X")
    set(LEGACY_OPENSSL TRUE)
elseif(REDHAT_RELEASE MATCHES "CentOS.*7\\.")
    # CentOS 7.x 환경 (OpenSSL 1.0.2)
    message(STATUS "Building for CentOS 7.x with OpenSSL 1.0.2")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DCENTOS_7X")
    set(LEGACY_OPENSSL TRUE)
elseif(REDHAT_RELEASE MATCHES "Rocky Linux.*9")
    # Rocky Linux 9 환경 (OpenSSL 3.x)
    message(STATUS "Building for Rocky Linux 9 with OpenSSL 3.x")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DROCKY_LINUX_9")
    set(MODERN_OPENSSL TRUE)
else()
    # 기타 환경 (OpenSSL 버전에 따라 결정)
    if(OPENSSL_MAJOR_VERSION STREQUAL "3")
        message(STATUS "Building with OpenSSL 3.x")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_3X")
        set(MODERN_OPENSSL TRUE)
    elseif(OPENSSL_MAJOR_VERSION STREQUAL "1.1")
        message(STATUS "Building with OpenSSL 1.1.x")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_11X")
        set(LEGACY_OPENSSL TRUE)
    else()
        message(STATUS "Building with OpenSSL 1.0.x or older")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DOPENSSL_10X")
        set(LEGACY_OPENSSL TRUE)
    endif()
    # 기본값으로 MODERN_OPENSSL 설정 (OpenSSL 3.x 대응)
    if(NOT DEFINED LEGACY_OPENSSL)
        set(MODERN_OPENSSL TRUE)
    endif()
endif()

# 데이터베이스 접속 정보 설정
# Option 1: 환경변수 사용 (CI/CD 환경에 권장)
# Option 2: 별도 설정 파일 사용 (로컬 개발에 권장)

# 설정 파일이 존재하면 include
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()

# 환경변수에서 데이터베이스 정보 읽기 (설정 파일보다 우선)
if(DEFINED ENV{DBSTRING})
    set(DBSTRING "$ENV{DBSTRING}")
endif()
if(DEFINED ENV{DBID})
    set(DBID "$ENV{DBID}")
endif()
if(DEFINED ENV{DBPASS})
    set(DBPASS "$ENV{DBPASS}")
endif()

# 필수 변수 확인
if(NOT DEFINED DBSTRING OR DBSTRING STREQUAL "")
    message(FATAL_ERROR "DBSTRING not set. Either set environment variables or create db_config.cmake from template")
endif()
if(NOT DEFINED DBID OR DBID STREQUAL "")
    message(FATAL_ERROR "DBID not set. Either set environment variables or create db_config.cmake from template")
endif()
if(NOT DEFINED DBPASS OR DBPASS STREQUAL "")
    message(FATAL_ERROR "DBPASS not set. Either set environment variables or create db_config.cmake from template")
endif()

# 디버그 정보 (실제 값은 출력하지 않음)
message(STATUS "Database configuration loaded successfully")

# Oracle 환경 설정
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Oracle Pro*C 컴파일러 찾기
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)

# Oracle Pro*C 전처리 함수
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)

    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # .cpp를 .pc로 복사
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # 특정 파일들에만 THREADS=YES 사용 (필요에 따라 조건 수정 가능)
    if(source_name MATCHES "logonDB|logonSession")
        set(THREADS_OPTION "THREADS=YES")
    else()
        set(THREADS_OPTION "")
    endif()

    # Oracle Pro*C 전처리
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${CMAKE_CURRENT_SOURCE_DIR}/inc
            include=${PROC_INCLUDE}
            include=${CMAKE_SOURCE_DIR}/../command_logon_ftk/inc
            ${THREADS_OPTION}
            CPP_SUFFIX=cpp
            CODE=CPP
            PARSE=NONE
            CTIMEOUT=3
            define=__sparc
            config=${PROC_CONFIG}
            SQLCHECK=SEMANTICS
            userid=${DBID}/${DBPASS}@${DBSTRING}
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# Include directories
include_directories(
    inc
    lib
    ${CMAKE_SOURCE_DIR}/../command_logon_ftk/inc
    ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/inc
    ${PROC_INCLUDE}
    $ENV{HOME}/library
)

# 라이브러리 디렉토리
link_directories(
    ${ORACLE_HOME}/lib
    /usr/lib64
    $ENV{HOME}/library
    ${ORAPP_DIR}
    ${LIBKSKYB_DIR}/lib
)

# 컴파일 정의
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# Oracle 컴파일 플래그
set(ORACLE_COMPILE_FLAGS 
    "-D_LOGON_MODE"
)

# 공통 라이브러리가 있다면 생성 (lib 디렉토리 확인 필요)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib)
    file(GLOB LIB_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/lib/*.cpp")
    # command_util.cpp는 makefile에서 사용하지 않으므로 제외
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*command_util\\.cpp$")
    # DatabaseORA_MMS.cpp는 Oracle Pro*C 파일이므로 별도 처리
    list(FILTER LIB_SOURCES EXCLUDE REGEX ".*DatabaseORA_MMS\\.cpp$")
    if(LIB_SOURCES)
        add_library(daemon_logon_lib STATIC ${LIB_SOURCES})
    endif()
endif()

# DatabaseORA_MMS Oracle Pro*C 라이브러리 (별도 처리)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)
    add_library(database_ora_mms STATIC)
    add_proc_source(database_ora_mms ${CMAKE_CURRENT_SOURCE_DIR}/lib/DatabaseORA_MMS.cpp)
    target_compile_options(database_ora_mms PRIVATE ${ORACLE_COMPILE_FLAGS})
endif()

# Database and process related executables
add_executable(logonDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/logonDB.cpp)
    # 파일 내용을 읽어서 EXEC SQL이 있는지 확인
    file(READ ${CMAKE_CURRENT_SOURCE_DIR}/src/logonDB.cpp LOGONDB_CONTENT)
    string(FIND "${LOGONDB_CONTENT}" "EXEC SQL" EXEC_SQL_FOUND)

    if(EXEC_SQL_FOUND GREATER -1)
        # EXEC SQL이 있으면 Pro*C 처리
        add_proc_source(logonDB ${CMAKE_CURRENT_SOURCE_DIR}/src/logonDB.cpp)
        target_compile_options(logonDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        # EXEC SQL이 없으면 일반 C++ 파일로 처리
        target_sources(logonDB PRIVATE src/logonDB.cpp)
    endif()
endif()

add_executable(logonSession)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/logonSession.cpp)
    # 파일 내용을 읽어서 EXEC SQL이 있는지 확인
    file(READ ${CMAKE_CURRENT_SOURCE_DIR}/src/logonSession.cpp LOGONSESSION_CONTENT)
    string(FIND "${LOGONSESSION_CONTENT}" "EXEC SQL" EXEC_SQL_FOUND)

    if(EXEC_SQL_FOUND GREATER -1)
        # EXEC SQL이 있으면 Pro*C 처리
        add_proc_source(logonSession ${CMAKE_CURRENT_SOURCE_DIR}/src/logonSession.cpp)
        target_compile_options(logonSession PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        # EXEC SQL이 없으면 일반 C++ 파일로 처리
        target_sources(logonSession PRIVATE src/logonSession.cpp)
    endif()
endif()

add_executable(adminProcess)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/adminProcess.cpp)
    target_sources(adminProcess PRIVATE src/adminProcess.cpp)
endif()

add_executable(monitorProcess)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/monitorProcess.cpp)
    target_sources(monitorProcess PRIVATE src/monitorProcess.cpp)
endif()

# Sender processes
#add_executable(senderFtalkCpool)
#if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkCpool.cpp)
#    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkCpool.pc OR
#       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkCpool.cpp)
#        add_proc_source(senderFtalkCpool ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkCpool.cpp)
#        target_compile_options(senderFtalkCpool PRIVATE ${ORACLE_COMPILE_FLAGS})
#    else()
#        target_sources(senderFtalkCpool PRIVATE src/senderFtalkCpool.cpp)
#    endif()
#endif()

add_executable(senderFtalkProDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkProDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkProDB.pc OR
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkProDB.cpp)
        add_proc_source(senderFtalkProDB ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFtalkProDB.cpp)
        target_compile_options(senderFtalkProDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(senderFtalkProDB PRIVATE src/senderFtalkProDB.cpp)
    endif()
endif()

#add_executable(senderMMSProcess)
#if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.cpp)
#    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.pc OR
#       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.cpp)
#        add_proc_source(senderMMSProcess ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSProcess.cpp)
#        target_compile_options(senderMMSProcess PRIVATE ${ORACLE_COMPILE_FLAGS})
#    else()
#        target_sources(senderMMSProcess PRIVATE src/senderMMSProcess.cpp)
#    endif()
#endif()

# Report processes
#add_executable(reportMMSProcess)
#if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.cpp)
#    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.pc OR
#       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.cpp)
#        add_proc_source(reportMMSProcess ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcess.cpp)
#        target_compile_options(reportMMSProcess PRIVATE ${ORACLE_COMPILE_FLAGS})
#    else()
#        target_sources(reportMMSProcess PRIVATE src/reportMMSProcess.cpp)
#    endif()
#endif()

# Admin process (adminUtil.cpp 포함)
add_executable(admin)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/admin.cpp)
    target_sources(admin PRIVATE src/admin.cpp)
endif()
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/lib/adminUtil.cpp)
    target_sources(admin PRIVATE lib/adminUtil.cpp)
endif()

# MMS DB processes
#add_executable(senderMMSDB)
#if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.cpp)
#    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.pc OR
#       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.cpp)
#        add_proc_source(senderMMSDB ${CMAKE_CURRENT_SOURCE_DIR}/src/senderMMSDB.cpp)
#        target_compile_options(senderMMSDB PRIVATE ${ORACLE_COMPILE_FLAGS})
#    else()
#        target_sources(senderMMSDB PRIVATE src/senderMMSDB.cpp)
#    endif()
#endif()

add_executable(reportMMSDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.pc OR
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.cpp)
        add_proc_source(reportMMSDB ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSDB.cpp)
        target_compile_options(reportMMSDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(reportMMSDB PRIVATE src/reportMMSDB.cpp)
    endif()
endif()

add_executable(reportMMSProcDB)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.cpp)
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.pc OR
       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.cpp)
        add_proc_source(reportMMSProcDB ${CMAKE_CURRENT_SOURCE_DIR}/src/reportMMSProcessDB.cpp)
        target_compile_options(reportMMSProcDB PRIVATE ${ORACLE_COMPILE_FLAGS})
    else()
        target_sources(reportMMSProcDB PRIVATE src/reportMMSProcessDB.cpp)
    endif()
endif()

# Additional executables based on src directory
#add_executable(senderFTkProcessDB)
#if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFTkProcessDB.cpp)
#    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFTkProcessDB.pc OR
#       PROC_EXECUTABLE AND EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFTkProcessDB.cpp)
#        add_proc_source(senderFTkProcessDB ${CMAKE_CURRENT_SOURCE_DIR}/src/senderFTkProcessDB.cpp)
#        target_compile_options(senderFTkProcessDB PRIVATE ${ORACLE_COMPILE_FLAGS})
#    else()
#        target_sources(senderFTkProcessDB PRIVATE src/senderFTkProcessDB.cpp)
#    endif()
#endif()

# 링크 라이브러리 설정
set(TARGETS logonSession logonDB admin reportMMSDB
            monitorProcess adminProcess 
            reportMMSProcDB senderFtalkProDB 
            )

foreach(target ${TARGETS})
    target_include_directories(${target} PRIVATE inc)

    # 외부 라이브러리는 이미 빌드된 것을 사용 (의존성 제거)

    # 공통 라이브러리가 있다면 링크
    if(TARGET daemon_logon_lib)
        target_link_libraries(${target} daemon_logon_lib)
    endif()

    # DatabaseORA_MMS Oracle Pro*C 라이브러리 링크
    if(TARGET database_ora_mms)
        target_link_libraries(${target} database_ora_mms)
    endif()

    # command_logon_ftk의 오브젝트 파일이 있다면 링크 (admin 제외)
    if(EXISTS ${CMAKE_SOURCE_DIR}/../command_logon_ftk/obj/sms_ctrlsub++.o AND NOT target STREQUAL "admin")
        target_link_libraries(${target} ${CMAKE_SOURCE_DIR}/../command_logon_ftk/obj/sms_ctrlsub++.o)
    endif()

    # libkskyb 라이브러리들 직접 링크 (makefile에서 사용하는 라이브러리들)
    target_link_libraries(${target}
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libksbase64.a
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libkssocket.a
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libksconfig.a
        ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb/lib/libksthread.a
    )

    # orapp 라이브러리 직접 링크
    target_link_libraries(${target} ${CMAKE_SOURCE_DIR}/../libsrc/orapp/liborapp.a)

    # Oracle 관련 실행파일에는 Oracle 라이브러리 링크
    if(target MATCHES "logonDB|logonSession|sender.*|report.*")
        target_link_libraries(${target} clntsh)
    endif()

    # OpenSSL crypto 라이브러리 링크 (Encrypt 클래스 사용)
    # 모든 환경에서 OpenSSL 3.x 호환성 문제를 해결하기 위해 강제로 레거시 설정 적용
    target_link_libraries(${target} crypto ssl)

    # OpenSSL 3.x에서 레거시 함수 사용을 위한 강력한 컴파일 플래그 추가
    target_compile_definitions(${target} PRIVATE
        OPENSSL_API_COMPAT=0x10100000L
        OPENSSL_SUPPRESS_DEPRECATED
        OPENSSL_NO_DEPRECATED_3_0
    )

    # 레거시 provider 활성화를 위한 추가 라이브러리 시도
    find_library(OPENSSL_LEGACY_LIB legacy PATHS /usr/lib64 /usr/lib /usr/lib/x86_64-linux-gnu)
    if(OPENSSL_LEGACY_LIB)
        target_link_libraries(${target} ${OPENSSL_LEGACY_LIB})
    endif()

    # OpenSSL 1.1 정적 라이브러리 시도
    find_library(OPENSSL_CRYPTO_11 crypto-1.1 PATHS /usr/lib64 /usr/lib /usr/lib/x86_64-linux-gnu)
    if(OPENSSL_CRYPTO_11)
        target_link_libraries(${target} ${OPENSSL_CRYPTO_11})
    endif()

    # 시스템 라이브러리 링크
    target_link_libraries(${target}
        pthread
        dl
    )
endforeach()

# Set output directories
set_target_properties(${TARGETS}
                      PROPERTIES
                      RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/bin"
)

# Add custom target for Makefile build
add_custom_target(makefile_build_daemon_logon
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak
    COMMENT "Building daemon logon with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add custom target for Makefile clean
add_custom_target(makefile_clean_daemon_logon
    COMMAND make -C ${CMAKE_CURRENT_SOURCE_DIR}/mak clean
    COMMENT "Cleaning daemon logon with original Makefile"
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# libkskyb와 orapp 라이브러리 경로 설정 (빌드하지 않고 기존 라이브러리 사용)
set(LIBKSKYB_DIR ${CMAKE_SOURCE_DIR}/../libsrc/libkskyb)
set(ORAPP_DIR ${CMAKE_SOURCE_DIR}/../libsrc/orapp)
